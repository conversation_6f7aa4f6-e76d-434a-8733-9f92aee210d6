# 用户偏好设置

- 用户决定新博客项目只保留前台的公共AI聊天助手功能，移除管理界面的AI助手功能。专注于迁移ChatAssistant组件和相关的聊天功能。
- 用户选择混合方案重新设计后台管理页面：保持前台的配色方案和基础风格，但优化管理界面，采用更适合数据管理的专业组件设计。
- 前端项目已配置 @ 别名指向 src 目录，并统一修改所有相对路径导入为 @ 导入。配置包括：1. vite.config.ts 中添加 alias 配置；2. tsconfig.app.json 中添加 paths 配置；3. 批量修改所有 '../' 和 '../../' 导入为 '@/' 导入。
- 用户要求全面优化前端代码质量，包括：1.创建通用工具和常量 2.重构组件结构 3.统一状态管理 4.优化样式系统 5.改进错误处理。要求在不改变代码逻辑的情况下提升可读性、规范化解耦、封装重复代码。不生成文档、不测试、不编译、不运行。
- 用户要求在现有frontend目录中创建Material Design 3博客前端，生成总结性Markdown文档，但不生成测试脚本、不编译、不运行
- 用户要求在 front 目录中创建新的 React 18 项目来解决版本冲突问题，生成总结性Markdown文档，但不生成测试脚本、不编译、不运行
- 用户要求基于Material Design 3设计风格使用M3官方组件构建博客前端，遵循UI/UX设计准则，合理构建前端页面。项目使用React 18 + Vite，已安装@material/web组件库。
- 用户要求将进度条应用到所有页面，选择方案1：在Layout组件中集成进度条。将进度条逻辑提取为独立的ScrollProgressBar组件，在Layout.tsx中集成，这样所有使用Layout的页面都会自动获得进度条。
- 用户要求创建完整的管理系统，包括：1.管理员登录页面 2.管理仪表板 3.文章管理页面 4.音乐管理页面 5.文件管理页面。使用Material Design 3风格，集成现有的认证系统和API。
