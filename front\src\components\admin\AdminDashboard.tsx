import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import adminApi from '@/services/adminApi';
import { DashboardStats } from '@/types/admin';
import './AdminDashboard.css';

const AdminDashboard: React.FC = () => {
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');
  const navigate = useNavigate();

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setIsLoading(true);
      const data = await adminApi.getDashboard();
      setStats(data);
    } catch (err) {
      setError('Failed to load dashboard data');
      console.error('Dashboard error:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const handleLogout = () => {
    adminApi.logout();
    navigate('/admin/login');
  };

  if (isLoading) {
    return (
      <div className="admin-dashboard loading">
        <md-circular-progress indeterminate />
        <p>Loading dashboard...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="admin-dashboard error">
        <md-icon>error</md-icon>
        <p>{error}</p>
        <md-filled-button onClick={loadDashboardData}>
          <md-icon slot="icon">refresh</md-icon>
          Retry
        </md-filled-button>
      </div>
    );
  }

  return (
    <div className="admin-dashboard">
      {/* Header */}
      <header className="dashboard-header">
        <div className="header-content">
          <div className="header-title">
            <md-icon className="dashboard-icon">dashboard</md-icon>
            <h1 className="md-typescale-headline-large">Admin Dashboard</h1>
          </div>
          <div className="header-actions">
            <md-outlined-button onClick={() => navigate('/')}>
              <md-icon slot="icon">public</md-icon>
              View Blog
            </md-outlined-button>
            <md-filled-button onClick={handleLogout}>
              <md-icon slot="icon">logout</md-icon>
              Logout
            </md-filled-button>
          </div>
        </div>
      </header>

      {/* Stats Cards */}
      <section className="stats-section">
        <div className="stats-grid">
          <div className="stat-card">
            <md-icon className="stat-icon">article</md-icon>
            <div className="stat-content">
              <h3 className="stat-number">{stats?.total_posts || 0}</h3>
              <p className="stat-label">Total Posts</p>
            </div>
          </div>

          <div className="stat-card">
            <md-icon className="stat-icon">category</md-icon>
            <div className="stat-content">
              <h3 className="stat-number">{stats?.total_categories || 0}</h3>
              <p className="stat-label">Categories</p>
            </div>
          </div>

          <div className="stat-card">
            <md-icon className="stat-icon">library_music</md-icon>
            <div className="stat-content">
              <h3 className="stat-number">{stats?.total_music_tracks || 0}</h3>
              <p className="stat-label">Music Tracks</p>
            </div>
          </div>
        </div>
      </section>

      {/* Quick Actions */}
      <section className="actions-section">
        <h2 className="section-title md-typescale-headline-medium">Quick Actions</h2>
        <div className="actions-grid">
          <div className="action-card" onClick={() => navigate('/admin/posts/new')}>
            <md-icon className="action-icon">add_circle</md-icon>
            <h3 className="action-title">Create New Post</h3>
            <p className="action-description">Write and publish a new blog post</p>
          </div>

          <div className="action-card" onClick={() => navigate('/admin/posts')}>
            <md-icon className="action-icon">edit</md-icon>
            <h3 className="action-title">Manage Posts</h3>
            <p className="action-description">Edit, delete, or organize existing posts</p>
          </div>

          <div className="action-card" onClick={() => navigate('/admin/music')}>
            <md-icon className="action-icon">upload</md-icon>
            <h3 className="action-title">Upload Music</h3>
            <p className="action-description">Add new music tracks to the player</p>
          </div>

          <div className="action-card" onClick={() => navigate('/admin/files')}>
            <md-icon className="action-icon">folder</md-icon>
            <h3 className="action-title">File Manager</h3>
            <p className="action-description">Manage uploaded images and files</p>
          </div>
        </div>
      </section>

      {/* Recent Posts */}
      {stats?.recent_posts && stats.recent_posts.length > 0 && (
        <section className="recent-section">
          <h2 className="section-title md-typescale-headline-medium">Recent Posts</h2>
          <div className="recent-posts">
            {stats.recent_posts.map((post) => (
              <div key={post.id} className="recent-post-item">
                <md-icon className="post-icon">article</md-icon>
                <div className="post-info">
                  <h4 className="post-title">{post.title}</h4>
                  <p className="post-date">{new Date(post.date).toLocaleDateString()}</p>
                </div>
                <md-icon-button onClick={() => navigate(`/admin/posts/${post.id}`)}>
                  <md-icon>edit</md-icon>
                </md-icon-button>
              </div>
            ))}
          </div>
        </section>
      )}
    </div>
  );
};

export default AdminDashboard;
