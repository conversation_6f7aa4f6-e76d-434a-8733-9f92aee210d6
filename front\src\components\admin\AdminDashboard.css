/* Admin Dashboard Styles */
.admin-dashboard {
  min-height: 100vh;
  background: var(--md-sys-color-background);
  color: var(--md-sys-color-on-background);
}

.admin-dashboard.loading,
.admin-dashboard.error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: var(--md-sys-spacing-4);
  text-align: center;
}

/* Header */
.dashboard-header {
  background: var(--md-sys-color-surface-container);
  border-bottom: 1px solid var(--md-sys-color-outline-variant);
  padding: var(--md-sys-spacing-6) var(--md-sys-spacing-8);
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-title {
  display: flex;
  align-items: center;
  gap: var(--md-sys-spacing-4);
}

.dashboard-icon {
  font-size: 32px;
  color: var(--md-sys-color-primary);
}

.header-title h1 {
  margin: 0;
  color: var(--md-sys-color-on-surface);
}

.header-actions {
  display: flex;
  gap: var(--md-sys-spacing-4);
}

/* Stats Section */
.stats-section {
  padding: var(--md-sys-spacing-8);
  max-width: 1200px;
  margin: 0 auto;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--md-sys-spacing-6);
}

.stat-card {
  background: var(--md-sys-color-surface-container-high);
  border-radius: var(--md-sys-shape-corner-large);
  padding: var(--md-sys-spacing-6);
  display: flex;
  align-items: center;
  gap: var(--md-sys-spacing-4);
  border: 1px solid var(--md-sys-color-outline-variant);
  transition: all var(--md-sys-motion-duration-short2) var(--md-sys-motion-easing-standard);
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  font-size: 32px;
  color: var(--md-sys-color-primary);
  background: rgba(var(--md-sys-color-primary-rgb), 0.1);
  padding: var(--md-sys-spacing-3);
  border-radius: var(--md-sys-shape-corner-medium);
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: var(--md-sys-typescale-headline-large-size);
  font-weight: var(--md-sys-typescale-headline-large-weight);
  color: var(--md-sys-color-on-surface);
  margin: 0 0 var(--md-sys-spacing-1) 0;
}

.stat-label {
  font-size: var(--md-sys-typescale-body-medium-size);
  color: var(--md-sys-color-on-surface-variant);
  margin: 0;
}

/* Actions Section */
.actions-section {
  padding: 0 var(--md-sys-spacing-8) var(--md-sys-spacing-8);
  max-width: 1200px;
  margin: 0 auto;
}

.section-title {
  margin: 0 0 var(--md-sys-spacing-6) 0;
  color: var(--md-sys-color-on-surface);
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--md-sys-spacing-6);
}

.action-card {
  background: var(--md-sys-color-surface-container);
  border-radius: var(--md-sys-shape-corner-large);
  padding: var(--md-sys-spacing-6);
  cursor: pointer;
  transition: all var(--md-sys-motion-duration-short2) var(--md-sys-motion-easing-standard);
  border: 1px solid var(--md-sys-color-outline-variant);
  text-align: center;
}

.action-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  background: var(--md-sys-color-surface-container-high);
}

.action-icon {
  font-size: 48px;
  color: var(--md-sys-color-primary);
  margin-bottom: var(--md-sys-spacing-4);
}

.action-title {
  font-size: var(--md-sys-typescale-title-large-size);
  font-weight: var(--md-sys-typescale-title-large-weight);
  color: var(--md-sys-color-on-surface);
  margin: 0 0 var(--md-sys-spacing-3) 0;
}

.action-description {
  font-size: var(--md-sys-typescale-body-medium-size);
  color: var(--md-sys-color-on-surface-variant);
  margin: 0;
  line-height: 1.5;
}

/* Recent Posts Section */
.recent-section {
  padding: 0 var(--md-sys-spacing-8) var(--md-sys-spacing-8);
  max-width: 1200px;
  margin: 0 auto;
}

.recent-posts {
  background: var(--md-sys-color-surface-container);
  border-radius: var(--md-sys-shape-corner-large);
  border: 1px solid var(--md-sys-color-outline-variant);
  overflow: hidden;
}

.recent-post-item {
  display: flex;
  align-items: center;
  gap: var(--md-sys-spacing-4);
  padding: var(--md-sys-spacing-4) var(--md-sys-spacing-6);
  border-bottom: 1px solid var(--md-sys-color-outline-variant);
  transition: background-color var(--md-sys-motion-duration-short2) var(--md-sys-motion-easing-standard);
}

.recent-post-item:last-child {
  border-bottom: none;
}

.recent-post-item:hover {
  background: var(--md-sys-color-surface-container-high);
}

.post-icon {
  color: var(--md-sys-color-primary);
  font-size: 24px;
}

.post-info {
  flex: 1;
}

.post-title {
  font-size: var(--md-sys-typescale-body-large-size);
  font-weight: var(--md-sys-typescale-body-large-weight);
  color: var(--md-sys-color-on-surface);
  margin: 0 0 var(--md-sys-spacing-1) 0;
}

.post-date {
  font-size: var(--md-sys-typescale-body-small-size);
  color: var(--md-sys-color-on-surface-variant);
  margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .dashboard-header {
    padding: var(--md-sys-spacing-4);
  }

  .header-content {
    flex-direction: column;
    gap: var(--md-sys-spacing-4);
    align-items: stretch;
  }

  .header-actions {
    justify-content: center;
  }

  .stats-section,
  .actions-section,
  .recent-section {
    padding: var(--md-sys-spacing-4);
  }

  .stats-grid,
  .actions-grid {
    grid-template-columns: 1fr;
  }

  .recent-post-item {
    padding: var(--md-sys-spacing-3) var(--md-sys-spacing-4);
  }
}

/* Animation */
.admin-dashboard {
  animation: dashboardFadeIn 0.4s var(--md-sys-motion-easing-emphasized);
}

@keyframes dashboardFadeIn {
  from {
    opacity: 0;
    transform: translateY(16px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
