use axum::{
    extract::{Path, Query, State},
    http::HeaderMap,
    response::<PERSON><PERSON>,
};
use axum_extra::extract::Multipart;
use serde::Deserialize;
use std::path::Path as StdPath;
use tokio::fs;
use tokio::io::AsyncWriteExt;

use crate::config::Settings;
use crate::database::Database;
use crate::handlers::health::HealthStatus;
use crate::models::{
    AiAssistRequest, AiAssistResponse, BlogPost, BlogPostCreate, BlogPostResponse, BlogPostUpdate,
    BlogPostsResponse, CreateMusicTrack, MusicListResponse, MusicQuery, MusicTrack,
    MusicUploadResponse, UpdateMusicTrack,
};
use crate::services::{AiService, AuthService, BlogService};
use crate::utils::{
    admin_success_response, blog_post_response, operation_success_response, AppError, A<PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,
};

#[derive(Debug, Deserialize)]
pub struct AdminPostsQuery {
    pub page: Option<i64>,
    pub limit: Option<i64>,
}

// Remove the check_admin_auth function since authentication is now handled by middleware

pub async fn get_dashboard(State(database): State<Database>) -> AppResult<Json<serde_json::Value>> {
    let blog_service = BlogService::new(database);
    let data = blog_service.get_dashboard_data().await?;

    Ok(Json(data))
}

pub async fn get_all_posts(
    Query(params): Query<AdminPostsQuery>,
    State(database): State<Database>,
) -> AppResult<Json<BlogPostsResponse>> {
    // Validate pagination parameters
    let (page, limit) = Validator::validate_pagination(params.page, params.limit)?;

    let blog_service = BlogService::new(database);
    let response = blog_service
        .get_all_posts_admin(Some(page), Some(limit))
        .await?;

    Ok(Json(response))
}

pub async fn get_post(
    Path(slug): Path<String>,
    State(database): State<Database>,
) -> AppResult<Json<BlogPost>> {
    // Validate slug format
    Validator::validate_slug(&slug)?;

    let blog_service = BlogService::new(database);

    match blog_service.get_post_by_slug(&slug).await? {
        Some(post) => Ok(Json(post)),
        None => Err(AppError::not_found(format!(
            "Post with slug '{}' not found",
            slug
        ))),
    }
}

pub async fn create_post(
    State(database): State<Database>,
    Json(post_data): Json<BlogPostCreate>,
) -> AppResult<Json<serde_json::Value>> {
    // Validate post data
    Validator::validate_title(&post_data.title)?;
    Validator::validate_excerpt(&post_data.excerpt)?;
    Validator::validate_content(&post_data.content)?;
    Validator::validate_categories(&post_data.categories)?;

    if let Some(ref slug) = post_data.slug {
        Validator::validate_slug(slug)?;
    }

    if let Some(ref date) = post_data.date {
        Validator::validate_date(date)?;
    }

    let blog_service = BlogService::new(database);
    let post = blog_service.create_post(post_data).await?;

    Ok(blog_post_response(Some(post), None))
}

pub async fn update_post(
    Path(slug): Path<String>,
    State(database): State<Database>,
    Json(post_data): Json<BlogPostUpdate>,
) -> AppResult<Json<serde_json::Value>> {
    // Validate slug format
    Validator::validate_slug(&slug)?;

    // Validate post data if provided
    if let Some(ref title) = post_data.title {
        Validator::validate_title(title)?;
    }
    if let Some(ref excerpt) = post_data.excerpt {
        Validator::validate_excerpt(excerpt)?;
    }
    if let Some(ref content) = post_data.content {
        Validator::validate_content(content)?;
    }
    if let Some(ref categories) = post_data.categories {
        Validator::validate_categories(categories)?;
    }
    if let Some(ref date) = post_data.date {
        Validator::validate_date(date)?;
    }

    let blog_service = BlogService::new(database);

    match blog_service.update_post(&slug, post_data).await? {
        Some(post) => Ok(blog_post_response(Some(post), None)),
        None => Ok(blog_post_response(
            None::<BlogPost>,
            Some("Post not found".to_string()),
        )),
    }
}

pub async fn delete_post(
    Path(slug): Path<String>,
    State(database): State<Database>,
) -> AppResult<Json<serde_json::Value>> {
    // Validate slug format
    Validator::validate_slug(&slug)?;

    let blog_service = BlogService::new(database);

    match blog_service.delete_post(&slug).await? {
        true => Ok(operation_success_response(None)),
        false => Ok(operation_success_response(Some(
            "Post not found".to_string(),
        ))),
    }
}

pub async fn get_categories(State(database): State<Database>) -> AppResult<Json<Vec<String>>> {
    let blog_service = BlogService::new(database);
    let categories = blog_service.get_categories().await?;

    Ok(Json(categories))
}

pub async fn ai_assist(
    State(database): State<Database>,
    Json(request): Json<AiAssistRequest>,
) -> AppResult<Json<AiAssistResponse>> {
    // Validate AI prompt
    Validator::validate_ai_prompt(&request.prompt)?;

    let settings = Settings::new()?;
    let ai_service = AiService::new(settings);

    let response = ai_service.ai_assist(request).await?;
    Ok(Json(response))
}

/// Upload image handler
pub async fn upload_image(
    State(database): State<Database>,
    mut multipart: Multipart,
) -> AppResult<Json<serde_json::Value>> {
    let settings = Settings::new()?;
    let upload_dir = StdPath::new(&settings.storage.upload_dir).join("images");

    // 确保上传目录存在
    fs::create_dir_all(&upload_dir)
        .await
        .map_err(|e| AppError::internal(format!("Failed to create upload directory: {}", e)))?;

    while let Some(field) = multipart
        .next_field()
        .await
        .map_err(|e| AppError::validation(format!("Failed to read multipart field: {}", e)))?
    {
        let name = field.name().unwrap_or("");

        if name == "image" {
            let filename = field
                .file_name()
                .ok_or_else(|| AppError::validation("No filename provided".to_string()))?
                .to_string();

            // 验证文件扩展名
            let extension = StdPath::new(&filename)
                .extension()
                .and_then(|ext| ext.to_str())
                .unwrap_or("")
                .to_lowercase();

            if !["jpg", "jpeg", "png", "webp", "gif"].contains(&extension.as_str()) {
                return Err(AppError::validation(
                    "Invalid file type. Only JPEG, PNG, WebP and GIF are allowed.".to_string(),
                ));
            }

            // 读取文件数据
            let data = field
                .bytes()
                .await
                .map_err(|e| AppError::validation(format!("Failed to read file data: {}", e)))?;

            // 验证文件大小
            if data.len() > settings.storage.max_file_size {
                return Err(AppError::validation(format!(
                    "File too large. Maximum size is {} bytes.",
                    settings.storage.max_file_size
                )));
            }

            // 生成唯一文件名
            let timestamp = std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap()
                .as_secs();
            let random: u32 = rand::random();
            let new_filename = format!("{}-{}.{}", timestamp, random, extension);

            // 保存文件
            let file_path = upload_dir.join(&new_filename);
            let mut file = fs::File::create(&file_path)
                .await
                .map_err(|e| AppError::internal(format!("Failed to create file: {}", e)))?;

            file.write_all(&data)
                .await
                .map_err(|e| AppError::internal(format!("Failed to write file: {}", e)))?;

            // 返回成功响应
            return Ok(Json(serde_json::json!({
                "success": true,
                "url": format!("/uploads/images/{}", new_filename),
                "filename": new_filename,
                "size": data.len()
            })));
        }
    }

    Err(AppError::validation(
        "No image file found in request".to_string(),
    ))
}

/// Get system status for admin dashboard
pub async fn get_system_status(
    State(database): State<Database>,
) -> AppResult<Json<serde_json::Value>> {
    // Get detailed health information
    let health_response =
        crate::handlers::health::health_detailed(axum::extract::State(database.clone())).await?;
    let health_data = health_response.0;

    // Extract relevant information for admin dashboard
    let server_status = match health_data.status {
        HealthStatus::Healthy => "online",
        HealthStatus::Degraded => "maintenance",
        HealthStatus::Unhealthy => "offline",
    };

    let database_status = match health_data.checks.database.status {
        crate::handlers::health::CheckStatus::Pass => "connected",
        crate::handlers::health::CheckStatus::Warn => "disconnected",
        crate::handlers::health::CheckStatus::Fail => "error",
    };

    let storage_usage = health_data
        .checks
        .disk
        .details
        .as_ref()
        .and_then(|details| details.get("usage_percent"))
        .and_then(|v| v.as_f64())
        .unwrap_or(0.0);

    let response = serde_json::json!({
        "success": true,
        "data": {
            "serverStatus": server_status,
            "databaseStatus": database_status,
            "storageUsage": storage_usage.round() as u32,
            "lastUpdated": chrono::Utc::now().to_rfc3339(),
            "uptime": health_data.uptime_seconds,
            "version": health_data.version,
            "metrics": health_data.metrics
        }
    });

    Ok(Json(response))
}

/// Get post markdown content for editing
pub async fn get_post_markdown(
    Path(slug): Path<String>,
    State(database): State<Database>,
) -> AppResult<Json<serde_json::Value>> {
    // Validate slug format
    Validator::validate_slug(&slug)?;

    let blog_service = BlogService::new(database);

    match blog_service.get_post_by_slug(&slug).await? {
        Some(post) => {
            let response = serde_json::json!({
                "success": true,
                "content": post.content.unwrap_or_default()
            });
            Ok(Json(response))
        }
        None => Err(AppError::not_found(format!(
            "Post with slug '{}' not found",
            slug
        ))),
    }
}

/// Get statistics trends for admin dashboard
pub async fn get_stats_trends(
    State(database): State<Database>,
) -> AppResult<Json<serde_json::Value>> {
    let blog_service = BlogService::new(database);
    let trends = blog_service.get_stats_trends().await?;

    let response = serde_json::json!({
        "success": true,
        "data": trends
    });

    Ok(Json(response))
}

// Music management handlers
pub async fn upload_music(
    State(_database): State<Database>,
    mut multipart: Multipart,
) -> AppResult<Json<MusicUploadResponse>> {
    let settings = Settings::new().map_err(|_| AppError::internal("Failed to load settings"))?;
    let upload_dir = std::path::Path::new(&settings.storage.upload_dir).join("music");

    // Create upload directory if it doesn't exist
    fs::create_dir_all(&upload_dir)
        .await
        .map_err(|_| AppError::internal("Failed to create upload directory"))?;

    while let Some(field) = multipart
        .next_field()
        .await
        .map_err(|_| AppError::validation("Invalid multipart data"))?
    {
        let name = field.name().unwrap_or("").to_string();

        if name == "music" {
            let filename = field
                .file_name()
                .ok_or_else(|| AppError::validation("No filename provided"))?
                .to_string();

            // Validate file extension
            let extension = StdPath::new(&filename)
                .extension()
                .and_then(|ext| ext.to_str())
                .unwrap_or("");

            if !["mp3", "wav", "ogg", "m4a"].contains(&extension.to_lowercase().as_str()) {
                return Err(AppError::validation(
                    "Invalid audio file format. Supported: mp3, wav, ogg, m4a",
                ));
            }

            let data = field
                .bytes()
                .await
                .map_err(|_| AppError::validation("Failed to read file data"))?;

            // Check file size (max 50MB for audio files)
            if data.len() > 50 * 1024 * 1024 {
                return Err(AppError::validation("File too large. Maximum size is 50MB"));
            }

            // Generate unique filename
            let timestamp = chrono::Utc::now().timestamp();
            let unique_filename = format!("{}_{}", timestamp, filename);
            let file_path = upload_dir.join(&unique_filename);

            // Write file
            let mut file = fs::File::create(&file_path)
                .await
                .map_err(|_| AppError::internal("Failed to create file"))?;
            file.write_all(&data)
                .await
                .map_err(|_| AppError::internal("Failed to write file"))?;

            let response = MusicUploadResponse {
                success: true,
                file_path: format!("/uploads/music/{}", unique_filename),
                file_name: filename,
                file_size: data.len() as i64,
                message: "Music file uploaded successfully".to_string(),
            };

            return Ok(Json(response));
        }
    }

    Err(AppError::validation("No music file found in request"))
}

pub async fn get_music_files(
    Query(params): Query<MusicQuery>,
    State(_database): State<Database>,
) -> AppResult<Json<MusicListResponse>> {
    // For now, return mock data. In a real implementation, this would query the database
    let tracks = vec![MusicTrack {
        id: 1,
        title: "Neon Dreams".to_string(),
        artist: "Synthwave Collective".to_string(),
        duration: "3:42".to_string(),
        file_path: "/uploads/music/neon-dreams.mp3".to_string(),
        file_name: "neon-dreams.mp3".to_string(),
        file_size: 5242880,
        cover_emoji: Some("🌆".to_string()),
        is_active: true,
        created_at: chrono::Utc::now(),
        updated_at: chrono::Utc::now(),
    }];

    let response = MusicListResponse {
        tracks,
        total: 1,
        page: params.page.unwrap_or(1),
        limit: params.limit.unwrap_or(10),
    };

    Ok(Json(response))
}

pub async fn create_music_entry(
    State(_database): State<Database>,
    Json(music_data): Json<CreateMusicTrack>,
) -> AppResult<Json<serde_json::Value>> {
    // Validate input
    if music_data.title.trim().is_empty() {
        return Err(AppError::validation("Title cannot be empty"));
    }

    if music_data.artist.trim().is_empty() {
        return Err(AppError::validation("Artist cannot be empty"));
    }

    // In a real implementation, this would insert into the database
    let response = serde_json::json!({
        "success": true,
        "message": "Music entry created successfully",
        "id": 1
    });

    Ok(Json(response))
}

pub async fn update_music_entry(
    Path(id): Path<i64>,
    State(_database): State<Database>,
    Json(_update_data): Json<UpdateMusicTrack>,
) -> AppResult<Json<serde_json::Value>> {
    // In a real implementation, this would update the database record
    let response = serde_json::json!({
        "success": true,
        "message": format!("Music entry {} updated successfully", id)
    });

    Ok(Json(response))
}

pub async fn delete_music_entry(
    Path(id): Path<i64>,
    State(_database): State<Database>,
) -> AppResult<Json<serde_json::Value>> {
    // In a real implementation, this would delete from the database and remove the file
    let response = serde_json::json!({
        "success": true,
        "message": format!("Music entry {} deleted successfully", id)
    });

    Ok(Json(response))
}
