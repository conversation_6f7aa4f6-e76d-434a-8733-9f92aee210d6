/* Admin Login Styles */
.admin-login {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 50%, #1a1a1a 100%);
  padding: var(--md-sys-spacing-6);
}

.login-container {
  background: var(--md-sys-color-surface-container);
  border-radius: var(--md-sys-shape-corner-extra-large);
  padding: var(--md-sys-spacing-10);
  width: 100%;
  max-width: 400px;
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.3),
    0 2px 8px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.login-header {
  text-align: center;
  margin-bottom: var(--md-sys-spacing-8);
}

.login-icon {
  font-size: 48px;
  color: var(--md-sys-color-primary);
  margin-bottom: var(--md-sys-spacing-4);
}

.login-title {
  color: var(--md-sys-color-on-surface);
  margin: 0 0 var(--md-sys-spacing-3) 0;
}

.login-subtitle {
  color: var(--md-sys-color-on-surface-variant);
  margin: 0;
  line-height: 1.5;
}

.login-form {
  display: flex;
  flex-direction: column;
  gap: var(--md-sys-spacing-6);
}

.form-field {
  width: 100%;
}

.token-input {
  width: 100%;
}

.error-message {
  display: flex;
  align-items: center;
  gap: var(--md-sys-spacing-3);
  color: var(--md-sys-color-error);
  background: rgba(var(--md-sys-color-error-rgb), 0.1);
  padding: var(--md-sys-spacing-4);
  border-radius: var(--md-sys-shape-corner-medium);
  border: 1px solid rgba(var(--md-sys-color-error-rgb), 0.3);
  font-size: var(--md-sys-typescale-body-small-size);
}

.error-message md-icon {
  font-size: 20px;
}

.login-button {
  width: 100%;
  height: 48px;
  margin-top: var(--md-sys-spacing-4);
}

.login-button md-circular-progress {
  --md-circular-progress-size: 20px;
}

.login-footer {
  margin-top: var(--md-sys-spacing-8);
  text-align: center;
  padding-top: var(--md-sys-spacing-6);
  border-top: 1px solid var(--md-sys-color-outline-variant);
}

/* Responsive Design */
@media (max-width: 480px) {
  .admin-login {
    padding: var(--md-sys-spacing-4);
  }

  .login-container {
    padding: var(--md-sys-spacing-8);
  }

  .login-icon {
    font-size: 40px;
  }
}

/* Focus and Hover States */
.login-button:hover:not([disabled]) {
  transform: translateY(-1px);
  box-shadow: 0 4px 16px rgba(var(--md-sys-color-primary-rgb), 0.3);
}

.login-button:active:not([disabled]) {
  transform: translateY(0);
}

/* Animation */
.login-container {
  animation: loginSlideIn 0.4s var(--md-sys-motion-easing-emphasized);
}

@keyframes loginSlideIn {
  from {
    opacity: 0;
    transform: translateY(24px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}
