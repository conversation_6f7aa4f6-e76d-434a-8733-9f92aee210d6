/* Ad<PERSON>gin Styles */
.admin-login {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #6750a4 0%, #7d5260 50%, #625b71 100%);
  padding: var(--md-sys-spacing-6);
  position: relative;
  overflow: hidden;
}

.admin-login::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
      circle at 20% 80%,
      rgba(103, 80, 164, 0.3) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 80% 20%,
      rgba(125, 82, 96, 0.3) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 40% 40%,
      rgba(98, 91, 113, 0.2) 0%,
      transparent 50%
    );
  z-index: 0;
}

.login-container {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  padding: 48px 40px;
  width: 100%;
  max-width: 420px;
  box-shadow: 0 24px 48px rgba(0, 0, 0, 0.2), 0 8px 16px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  z-index: 1;
}

.login-header {
  text-align: center;
  margin-bottom: 32px;
}

.login-icon {
  font-size: 56px;
  color: #6750a4;
  margin-bottom: 16px;
  display: block;
}

.login-title {
  color: #1d1b20;
  margin: 0 0 12px 0;
  font-size: 32px;
  font-weight: 400;
  letter-spacing: -0.5px;
}

.login-subtitle {
  color: #49454f;
  margin: 0;
  line-height: 1.5;
  font-size: 16px;
}

.login-form {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.form-field {
  width: 100%;
}

.token-input {
  width: 100%;
  --md-outlined-text-field-container-shape: 12px;
}

.error-message {
  display: flex;
  align-items: center;
  gap: 12px;
  color: #ba1a1a;
  background: rgba(186, 26, 26, 0.08);
  padding: 16px;
  border-radius: 12px;
  border: 1px solid rgba(186, 26, 26, 0.2);
  font-size: 14px;
}

.error-message md-icon {
  font-size: 20px;
}

.login-button {
  width: 100%;
  height: 56px;
  margin-top: 8px;
  --md-filled-button-container-shape: 28px;
  font-size: 16px;
  font-weight: 500;
}

.login-button md-circular-progress {
  --md-circular-progress-size: 20px;
}

.login-footer {
  margin-top: 32px;
  text-align: center;
  padding-top: 24px;
  border-top: 1px solid rgba(73, 69, 79, 0.2);
}

/* Responsive Design */
@media (max-width: 480px) {
  .admin-login {
    padding: 16px;
  }

  .login-container {
    padding: 32px 24px;
    max-width: 100%;
  }

  .login-icon {
    font-size: 48px;
  }

  .login-title {
    font-size: 28px;
  }
}

/* Focus and Hover States */
.login-button:hover:not([disabled]) {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(103, 80, 164, 0.3);
}

.login-button:active:not([disabled]) {
  transform: translateY(0);
}

/* Animation */
.login-container {
  animation: loginSlideIn 0.6s cubic-bezier(0.2, 0, 0, 1);
}

@keyframes loginSlideIn {
  from {
    opacity: 0;
    transform: translateY(32px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Additional polish */
.login-container:hover {
  transform: translateY(-4px);
  box-shadow: 0 32px 64px rgba(0, 0, 0, 0.25), 0 16px 32px rgba(0, 0, 0, 0.15);
  transition: all 0.3s cubic-bezier(0.2, 0, 0, 1);
}
