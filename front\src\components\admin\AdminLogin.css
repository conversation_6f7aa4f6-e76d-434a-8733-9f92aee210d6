/* Ad<PERSON>gin Styles */
.admin-login {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #6750a4 0%, #7d5260 50%, #625b71 100%);
  padding: var(--md-sys-spacing-6);
  position: relative;
  overflow: hidden;
}

.admin-login::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
      circle at 20% 80%,
      rgba(103, 80, 164, 0.3) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 80% 20%,
      rgba(125, 82, 96, 0.3) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 40% 40%,
      rgba(98, 91, 113, 0.2) 0%,
      transparent 50%
    );
  z-index: 0;
}

.login-container {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  padding: 48px 40px;
  width: 100%;
  max-width: 420px;
  box-shadow: 0 24px 48px rgba(0, 0, 0, 0.2), 0 8px 16px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  z-index: 1;
}

.login-header {
  text-align: center;
  margin-bottom: 32px;
}

.login-icon {
  font-size: 56px;
  color: #6750a4;
  margin-bottom: 16px;
  display: block;
}

.login-title {
  color: #1d1b20;
  margin: 0 0 12px 0;
  font-size: 32px;
  font-weight: 400;
  letter-spacing: -0.5px;
}

.login-subtitle {
  color: #49454f;
  margin: 0;
  line-height: 1.5;
  font-size: 16px;
}

.login-form {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.form-field {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.input-label {
  font-size: 14px;
  font-weight: 500;
  color: #49454f;
  margin-bottom: 4px;
}

.token-input {
  width: 100%;
  height: 56px;
  padding: 16px;
  border: 2px solid #79747e;
  border-radius: 12px;
  font-size: 16px;
  font-family: "Roboto", sans-serif;
  background: transparent;
  color: #1d1b20;
  transition: all 0.2s ease;
  outline: none;
}

.token-input:focus {
  border-color: #6750a4;
  box-shadow: 0 0 0 1px #6750a4;
}

.token-input:hover:not(:focus) {
  border-color: #49454f;
}

.token-input::placeholder {
  color: #79747e;
}

.error-message {
  display: flex;
  align-items: center;
  gap: 12px;
  color: #ba1a1a;
  background: rgba(186, 26, 26, 0.08);
  padding: 16px;
  border-radius: 12px;
  border: 1px solid rgba(186, 26, 26, 0.2);
  font-size: 14px;
}

.error-message md-icon {
  font-size: 20px;
}

.login-button {
  width: 100%;
  height: 56px;
  margin-top: 8px;
  --md-filled-button-container-shape: 28px;
  --md-filled-button-container-color: #6750a4;
  --md-filled-button-label-text-color: #ffffff;
  --md-filled-button-hover-container-color: #7965af;
  --md-filled-button-pressed-container-color: #5d4e98;
  font-size: 16px;
  font-weight: 500;
  background: #6750a4 !important;
  color: white !important;
  border: none;
  border-radius: 28px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.login-button md-circular-progress {
  --md-circular-progress-size: 20px;
  --md-circular-progress-active-indicator-color: #ffffff;
}

.login-button:disabled {
  background: #e8e8e8 !important;
  color: #9e9e9e !important;
  cursor: not-allowed;
}

.login-footer {
  margin-top: 32px;
  text-align: center;
  padding-top: 24px;
  border-top: 1px solid rgba(73, 69, 79, 0.2);
}

.login-footer md-text-button {
  --md-text-button-label-text-color: #6750a4;
  --md-text-button-hover-label-text-color: #7965af;
  --md-text-button-pressed-label-text-color: #5d4e98;
  --md-text-button-container-shape: 20px;
  color: #6750a4 !important;
  font-weight: 500;
}

/* Responsive Design */
@media (max-width: 480px) {
  .admin-login {
    padding: 16px;
  }

  .login-container {
    padding: 32px 24px;
    max-width: 100%;
  }

  .login-icon {
    font-size: 48px;
  }

  .login-title {
    font-size: 28px;
  }
}

/* Focus and Hover States */
.login-button:hover:not([disabled]) {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(103, 80, 164, 0.3);
  background: #7965af !important;
}

.login-button:active:not([disabled]) {
  transform: translateY(0);
  background: #5d4e98 !important;
}

.login-footer md-text-button:hover {
  background: rgba(103, 80, 164, 0.08) !important;
}

/* Animation */
.login-container {
  animation: loginSlideIn 0.6s cubic-bezier(0.2, 0, 0, 1);
}

@keyframes loginSlideIn {
  from {
    opacity: 0;
    transform: translateY(32px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Additional polish */
.login-container:hover {
  transform: translateY(-4px);
  box-shadow: 0 32px 64px rgba(0, 0, 0, 0.25), 0 16px 32px rgba(0, 0, 0, 0.15);
  transition: all 0.3s cubic-bezier(0.2, 0, 0, 1);
}

/* Ensure Material Design components are visible */
md-filled-button,
md-text-button,
md-outlined-text-field,
md-circular-progress,
md-icon {
  display: inline-flex !important;
  visibility: visible !important;
}

/* Fix for Material Web Components not loading */
.login-button:not(md-filled-button) {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 0 24px;
  text-decoration: none;
  font-family: "Roboto", sans-serif;
}

.login-footer button:not(md-text-button) {
  background: none;
  border: none;
  color: #6750a4;
  cursor: pointer;
  padding: 12px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  transition: background-color 0.2s ease;
}

.login-footer button:not(md-text-button):hover {
  background: rgba(103, 80, 164, 0.08);
}

/* Native button styles for fallback */
.login-button {
  display: flex !important;
  align-items: center;
  justify-content: center;
  gap: 8px;
  width: 100%;
  height: 56px;
  margin-top: 8px;
  background: #6750a4;
  color: white;
  border: none;
  border-radius: 28px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  font-family: "Roboto", sans-serif;
}

.login-button:hover:not(:disabled) {
  background: #7965af;
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(103, 80, 164, 0.3);
}

.login-button:active:not(:disabled) {
  background: #5d4e98;
  transform: translateY(0);
}

.login-button:disabled {
  background: #e8e8e8;
  color: #9e9e9e;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.loading-spinner {
  animation: spin 1s linear infinite;
  font-size: 18px;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.login-icon,
.back-icon {
  font-size: 18px;
  font-weight: bold;
}

.login-footer button {
  background: none;
  border: none;
  color: #6750a4;
  cursor: pointer;
  padding: 12px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  transition: background-color 0.2s ease;
  font-family: "Roboto", sans-serif;
}

.login-footer button:hover {
  background: rgba(103, 80, 164, 0.08);
}
