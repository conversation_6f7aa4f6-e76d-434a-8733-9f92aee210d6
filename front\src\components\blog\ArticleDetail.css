/* Article Detail Styles */

.article-detail {
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: 1fr 280px;
  gap: var(--md-sys-spacing-8);
  animation: articleSlideIn var(--md-sys-motion-duration-medium4)
    var(--md-sys-motion-easing-emphasized);
}

.article-main {
  min-width: 0; /* Prevent grid overflow */
  margin-left: 30px; /* Move article content to the right */
}

.article-sidebar {
  position: fixed;
  top: 0;
  right: 0;
  width: 280px;
  height: 100vh;
  z-index: 10;
  pointer-events: none;
}

/* Article Header */
.article-header {
  margin-bottom: var(--md-sys-spacing-8);
}

/* Article Hero Image */
.article-hero-image {
  width: 100%;
  height: 400px;
  border-radius: var(--md-sys-shape-corner-large);
  overflow: hidden;
  margin-bottom: var(--md-sys-spacing-6);
  box-shadow: var(--md-sys-elevation-level2);
}

.article-hero-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Article Meta */
.article-meta {
  display: flex;
  align-items: center;
  gap: var(--md-sys-spacing-4);
  margin-bottom: var(--md-sys-spacing-6);
  flex-wrap: wrap;
}

.article-category {
  --md-assist-chip-container-color: var(--md-sys-color-primary-container);
  --md-assist-chip-label-text-color: var(--md-sys-color-on-primary-container);
  --md-assist-chip-icon-color: var(--md-sys-color-on-primary-container);
}

.article-date,
.article-read-time {
  color: var(--md-sys-color-on-surface-variant);
}

/* Article Title */
.article-title {
  margin: 0 0 var(--md-sys-spacing-6);
  color: var(--md-sys-color-on-surface);
}

/* Article Author */
.article-author-info {
  display: flex;
  align-items: center;
  gap: var(--md-sys-spacing-2);
  margin-bottom: var(--md-sys-spacing-6);
}

.author-icon {
  color: var(--md-sys-color-on-surface-variant);
}

.author-name {
  color: var(--md-sys-color-on-surface);
}

/* Article Tags */
.article-tags {
  display: flex;
  flex-wrap: wrap;
  gap: var(--md-sys-spacing-2);
  margin-bottom: var(--md-sys-spacing-8);
}

.article-tag {
  --md-filter-chip-container-color: var(--md-sys-color-surface-container-high);
  --md-filter-chip-label-text-color: var(--md-sys-color-on-surface);
}

/* Article Content */
.article-content {
  margin: var(--md-sys-spacing-8) 0;
}

.article-body {
  line-height: 1.7;
  color: var(--md-sys-color-on-surface);
}

/* Article Outline */
.article-outline {
  background: transparent;
  padding: var(--md-sys-spacing-6);
  margin: var(--md-sys-spacing-6);
  height: calc(100vh - var(--md-sys-spacing-12));
  overflow-y: auto;
  pointer-events: auto;
}

.outline-title {
  margin: 0 0 var(--md-sys-spacing-6);
  color: var(--md-sys-color-on-surface);
  font-weight: 600;
  font-size: var(--md-sys-typescale-title-large-size);
  display: flex;
  align-items: center;
  gap: var(--md-sys-spacing-3);
}

.outline-title md-icon {
  font-size: 24px;
  color: var(--md-sys-color-primary);
}

.outline-list {
  list-style: none;
  margin: 0;
  padding: 0;
}

.outline-item {
  margin-bottom: var(--md-sys-spacing-2);
}

.outline-link {
  display: block;
  padding: var(--md-sys-spacing-3) var(--md-sys-spacing-4);
  color: var(--md-sys-color-on-surface-variant);
  text-decoration: none;
  border-radius: var(--md-sys-shape-corner-medium);
  font-size: var(--md-sys-typescale-body-medium-size);
  line-height: var(--md-sys-typescale-body-medium-line-height);
  transition: all var(--md-sys-motion-duration-short2)
    var(--md-sys-motion-easing-standard);
  position: relative;
  border-left: 3px solid transparent;
  font-weight: 500;
}

.outline-link:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: var(--md-sys-color-on-surface);
  transform: translateX(4px);
}

.outline-link.active {
  background-color: rgba(204, 182, 255, 0.2);
  color: #ccb6ff;
  border-left-color: #ccb6ff;
  transform: translateX(8px);
}

.outline-link.active::before {
  content: "";
  position: absolute;
  right: var(--md-sys-spacing-3);
  top: 50%;
  transform: translateY(-50%);
  width: 6px;
  height: 6px;
  background-color: #ccb6ff;
  border-radius: 50%;
}

.outline-link.level-2 {
  padding-left: var(--md-sys-spacing-6);
  font-size: var(--md-sys-typescale-body-small-size);
  font-weight: 400;
}

.outline-link.level-3 {
  padding-left: var(--md-sys-spacing-8);
  font-size: var(--md-sys-typescale-label-large-size);
  font-weight: 400;
  opacity: 0.8;
}

/* Article Body Typography */
.article-body h2 {
  font-family: var(--md-sys-typescale-headline-medium-font);
  font-size: var(--md-sys-typescale-headline-medium-size);
  font-weight: var(--md-sys-typescale-headline-medium-weight);
  line-height: var(--md-sys-typescale-headline-medium-line-height);
  color: var(--md-sys-color-on-surface);
  margin: var(--md-sys-spacing-8) 0 var(--md-sys-spacing-4);
}

.article-body h3 {
  font-family: var(--md-sys-typescale-headline-small-font);
  font-size: var(--md-sys-typescale-headline-small-size);
  font-weight: var(--md-sys-typescale-headline-small-weight);
  line-height: var(--md-sys-typescale-headline-small-line-height);
  color: var(--md-sys-color-on-surface);
  margin: var(--md-sys-spacing-6) 0 var(--md-sys-spacing-3);
}

.article-body p {
  margin: 0 0 var(--md-sys-spacing-4);
  color: var(--md-sys-color-on-surface);
}

.article-body ul,
.article-body ol {
  margin: 0 0 var(--md-sys-spacing-4);
  padding-left: var(--md-sys-spacing-6);
  color: var(--md-sys-color-on-surface);
}

.article-body li {
  margin-bottom: var(--md-sys-spacing-2);
}

.article-body strong {
  font-weight: 600;
  color: var(--md-sys-color-on-surface);
}

.article-body pre {
  background-color: var(--md-sys-color-surface-container-highest);
  color: var(--md-sys-color-on-surface);
  padding: var(--md-sys-spacing-4);
  border-radius: var(--md-sys-shape-corner-medium);
  overflow-x: auto;
  margin: var(--md-sys-spacing-4) 0;
  border: 1px solid var(--md-sys-color-outline-variant);
}

.article-body code {
  background-color: var(--md-sys-color-surface-container-high);
  color: var(--md-sys-color-on-surface);
  padding: var(--md-sys-spacing-1) var(--md-sys-spacing-2);
  border-radius: var(--md-sys-shape-corner-extra-small);
  font-family: "Roboto Mono", monospace;
  font-size: 0.9em;
}

.article-body pre code {
  background: none;
  padding: 0;
  border-radius: 0;
}

/* See More Section */
.see-more-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  margin: var(--md-sys-spacing-12) 0;
  padding: var(--md-sys-spacing-8);
}

.see-more-title {
  margin: 0 0 var(--md-sys-spacing-2);
  color: var(--md-sys-color-on-surface);
}

.see-more-description {
  margin: 0 0 var(--md-sys-spacing-4);
  color: var(--md-sys-color-on-surface-variant);
}

.see-more-button {
  --md-elevated-button-container-color: #ccb6ff;
  --md-elevated-button-label-text-color: #1f1f1f;
  --md-elevated-button-container-shape: 20px;
  --md-elevated-button-hover-state-layer-color: rgba(31, 31, 31, 0.08);
  --md-elevated-button-pressed-state-layer-color: rgba(31, 31, 31, 0.12);
  --md-elevated-button-icon-color: #1f1f1f;
  --md-elevated-button-container-elevation: 1;
  --md-elevated-button-hover-container-elevation: 2;
  --md-elevated-button-pressed-container-elevation: 1;
  --md-elevated-button-label-text-size: 1rem;
  --md-elevated-button-label-text-weight: 500;
  min-width: 180px;
}

/* Related Articles */
.related-articles {
  border-top: 1px solid var(--md-sys-color-outline-variant);
  padding-top: var(--md-sys-spacing-8);
}

.related-articles-title {
  margin: 0 0 var(--md-sys-spacing-6);
  color: var(--md-sys-color-on-surface);
  font-weight: 500;
}

.related-articles-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--md-sys-spacing-6);
}

.related-article-card {
  animation: relatedCardSlideIn var(--md-sys-motion-duration-medium3)
    var(--md-sys-motion-easing-emphasized);
}

/* Loading and Error States */
.article-detail-loading,
.article-detail-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: var(--md-sys-spacing-4);
  min-height: 400px;
  text-align: center;
  color: var(--md-sys-color-on-surface-variant);
}

.error-icon {
  font-size: 64px;
  color: var(--md-sys-color-error);
  margin-bottom: var(--md-sys-spacing-2);
}

.article-hero-image {
  width: 100%;
  height: 400px;
  border-radius: var(--md-sys-shape-corner-large);
  overflow: hidden;
  margin-bottom: var(--md-sys-spacing-6);
  box-shadow: var(--md-sys-elevation-level2);
}

.article-hero-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.article-header-content {
  display: flex;
  flex-direction: column;
  gap: var(--md-sys-spacing-4);
}

.article-meta {
  display: flex;
  align-items: center;
  gap: var(--md-sys-spacing-3);
  flex-wrap: wrap;
}

.article-category {
  --md-assist-chip-container-color: var(--md-sys-color-secondary-container);
  --md-assist-chip-label-text-color: var(--md-sys-color-on-secondary-container);
  --md-assist-chip-icon-color: var(--md-sys-color-on-secondary-container);
}

.article-date,
.article-read-time {
  color: var(--md-sys-color-on-surface-variant);
}

.article-date::after {
  content: "•";
  margin: 0 var(--md-sys-spacing-2);
  color: var(--md-sys-color-outline);
}

.article-title {
  margin: 0;
  color: var(--md-sys-color-on-surface);
  font-weight: 500;
  line-height: 1.2;
}

.article-author-info {
  display: flex;
  align-items: center;
  gap: var(--md-sys-spacing-2);
  color: var(--md-sys-color-on-surface-variant);
}

.author-icon {
  font-size: 20px;
}

.author-name {
  font-weight: 500;
  color: var(--md-sys-color-on-surface);
}

.article-tags {
  display: flex;
  align-items: center;
  gap: var(--md-sys-spacing-2);
  flex-wrap: wrap;
}

.article-tag {
  --md-filter-chip-container-color: var(
    --md-sys-color-surface-container-highest
  );
  --md-filter-chip-label-text-color: var(--md-sys-color-on-surface-variant);
  --md-filter-chip-disabled-container-color: var(
    --md-sys-color-surface-container-highest
  );
  --md-filter-chip-disabled-label-text-color: var(
    --md-sys-color-on-surface-variant
  );
}

/* Article Content */
.article-content {
  margin-bottom: var(--md-sys-spacing-12);
}

.article-body {
  line-height: 1.7;
  color: var(--md-sys-color-on-surface);
}

.article-body h2 {
  font-family: var(--md-sys-typescale-headline-medium-font);
  font-size: var(--md-sys-typescale-headline-medium-size);
  font-weight: var(--md-sys-typescale-headline-medium-weight);
  line-height: var(--md-sys-typescale-headline-medium-line-height);
  color: var(--md-sys-color-on-surface);
  margin: var(--md-sys-spacing-8) 0 var(--md-sys-spacing-4);
}

.article-body h3 {
  font-family: var(--md-sys-typescale-headline-small-font);
  font-size: var(--md-sys-typescale-headline-small-size);
  font-weight: var(--md-sys-typescale-headline-small-weight);
  line-height: var(--md-sys-typescale-headline-small-line-height);
  color: var(--md-sys-color-on-surface);
  margin: var(--md-sys-spacing-6) 0 var(--md-sys-spacing-3);
}

.article-body p {
  margin: 0 0 var(--md-sys-spacing-4);
  color: var(--md-sys-color-on-surface);
}

.article-body ul,
.article-body ol {
  margin: 0 0 var(--md-sys-spacing-4);
  padding-left: var(--md-sys-spacing-6);
  color: var(--md-sys-color-on-surface);
}

.article-body li {
  margin-bottom: var(--md-sys-spacing-2);
}

.article-body strong {
  font-weight: 600;
  color: var(--md-sys-color-on-surface);
}

.article-body pre {
  background-color: var(--md-sys-color-surface-container-highest);
  color: var(--md-sys-color-on-surface);
  padding: var(--md-sys-spacing-4);
  border-radius: var(--md-sys-shape-corner-medium);
  overflow-x: auto;
  margin: var(--md-sys-spacing-4) 0;
  border: 1px solid var(--md-sys-color-outline-variant);
}

.article-body code {
  background-color: var(--md-sys-color-surface-container-high);
  color: var(--md-sys-color-on-surface);
  padding: var(--md-sys-spacing-1) var(--md-sys-spacing-2);
  border-radius: var(--md-sys-shape-corner-extra-small);
  font-family: "Roboto Mono", monospace;
  font-size: 0.9em;
}

.article-body pre code {
  background: none;
  padding: 0;
  border-radius: 0;
}

/* See More Section */
.see-more-section {
  display: flex;
  justify-content: center;
  margin: var(--md-sys-spacing-12) 0;
  padding: var(--md-sys-spacing-4) 0;
}

.see-more-button {
  --md-elevated-button-container-color: #a855f7;
  --md-elevated-button-label-text-color: #1f1f1f;
  --md-elevated-button-container-shape: 20px;
  --md-elevated-button-hover-state-layer-color: rgba(31, 31, 31, 0.08);
  --md-elevated-button-pressed-state-layer-color: rgba(31, 31, 31, 0.12);
  --md-elevated-button-icon-color: #1f1f1f;
  --md-elevated-button-container-elevation: 1;
  --md-elevated-button-hover-container-elevation: 2;
  --md-elevated-button-pressed-container-elevation: 1;
  --md-elevated-button-label-text-size: 1rem;
  --md-elevated-button-label-text-weight: 500;
  min-width: 180px;
}

/* Related Articles */
.related-articles {
  border-top: 1px solid var(--md-sys-color-outline-variant);
  padding-top: var(--md-sys-spacing-8);
}

.related-articles-title {
  margin: 0 0 var(--md-sys-spacing-6);
  color: var(--md-sys-color-on-surface);
  font-weight: 500;
}

.related-articles-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--md-sys-spacing-6);
}

.related-article-card {
  animation: relatedCardSlideIn var(--md-sys-motion-duration-medium3)
    var(--md-sys-motion-easing-emphasized);
}

/* Animations */
@keyframes articleSlideIn {
  from {
    opacity: 0;
    transform: translateY(32px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes relatedCardSlideIn {
  from {
    opacity: 0;
    transform: translateY(16px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Design */
@media (max-width: 1024px) {
  .article-detail {
    grid-template-columns: 1fr;
    max-width: 900px;
    margin: 0 auto;
  }

  .article-main {
    margin-left: 0; /* Remove right margin on small screens */
  }

  .article-sidebar {
    display: none;
  }
}

@media (max-width: 600px) {
  .article-detail {
    margin: 0;
    max-width: 100%;
    padding: 0 var(--md-sys-spacing-4);
  }

  .article-hero-image {
    height: 250px;
    margin-bottom: var(--md-sys-spacing-4);
    border-radius: var(--md-sys-shape-corner-medium);
  }

  .article-title {
    font-size: var(--md-sys-typescale-headline-large-size);
    line-height: var(--md-sys-typescale-headline-large-line-height);
  }

  .article-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--md-sys-spacing-2);
  }

  .article-body h2 {
    font-size: var(--md-sys-typescale-headline-small-size);
    line-height: var(--md-sys-typescale-headline-small-line-height);
    margin: var(--md-sys-spacing-6) 0 var(--md-sys-spacing-3);
  }

  .article-body h3 {
    font-size: var(--md-sys-typescale-title-large-size);
    line-height: var(--md-sys-typescale-title-large-line-height);
    margin: var(--md-sys-spacing-4) 0 var(--md-sys-spacing-2);
  }

  .article-body pre {
    padding: var(--md-sys-spacing-3);
    font-size: 0.9em;
  }

  .related-articles-grid {
    grid-template-columns: 1fr;
    gap: var(--md-sys-spacing-4);
  }

  .see-more-section {
    margin: var(--md-sys-spacing-8) 0;
  }

  .see-more-button {
    padding: 14px 32px;
    font-size: 0.9rem;
    min-width: 160px;
  }
}

@media (max-width: 480px) {
  .article-header-actions {
    margin-bottom: var(--md-sys-spacing-3);
  }

  .article-hero-image {
    height: 200px;
  }

  .article-tags {
    gap: var(--md-sys-spacing-1);
  }

  .see-more-section {
    margin: var(--md-sys-spacing-6) 0;
    padding: var(--md-sys-spacing-4);
    border-radius: var(--md-sys-shape-corner-medium);
  }

  .see-more-title {
    font-size: var(--md-sys-typescale-title-large-size);
  }

  .see-more-description {
    font-size: var(--md-sys-typescale-body-small-size);
    line-height: 1.4;
  }

  .see-more-button {
    margin-top: var(--md-sys-spacing-1);
  }
}

/* Modern Article Detail Responsive Styles */
@media (max-width: 768px) {
  .article-detail-container {
    padding: 0 16px;
  }

  .article-navigation {
    padding: 16px 0;
    margin-bottom: 24px;
  }

  .back-button {
    font-size: 0.875rem;
  }

  .article-hero-content {
    padding: 32px 24px;
  }

  .article-title {
    font-size: 2rem;
  }

  .article-excerpt {
    font-size: 1.125rem;
  }

  .article-content {
    padding: 32px 24px;
  }

  .article-text {
    font-size: 1rem;
  }

  .article-footer {
    padding: 32px 24px;
  }

  .article-engagement {
    flex-direction: column;
    align-items: flex-start;
    gap: 24px;
  }
}

@media (max-width: 480px) {
  .article-hero-image {
    height: 250px;
  }

  .article-hero-content {
    padding: 24px 16px;
  }

  .article-title {
    font-size: 1.75rem;
  }

  .article-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .article-content {
    padding: 24px 16px;
  }

  .article-footer {
    padding: 24px 16px;
  }
}

/* Print Styles */
@media print {
  .article-header-actions,
  .related-articles {
    display: none;
  }

  .article-detail {
    max-width: none;
  }

  .article-body {
    color: black;
  }

  .article-body h2,
  .article-body h3 {
    color: black;
  }
}
