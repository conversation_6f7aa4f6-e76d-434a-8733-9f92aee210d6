use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::FromRow;

#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct MusicTrack {
    pub id: i64,
    pub title: String,
    pub artist: String,
    pub duration: String, // Format: "3:42"
    pub file_path: String, // Path to the audio file
    pub file_name: String, // Original filename
    pub file_size: i64,    // File size in bytes
    pub cover_emoji: Option<String>, // Emoji for cover art
    pub is_active: bool,   // Whether the track is active/published
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateMusicTrack {
    pub title: String,
    pub artist: String,
    pub duration: String,
    pub file_path: String,
    pub file_name: String,
    pub file_size: i64,
    pub cover_emoji: Option<String>,
    pub is_active: Option<bool>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateMusicTrack {
    pub title: Option<String>,
    pub artist: Option<String>,
    pub duration: Option<String>,
    pub cover_emoji: Option<String>,
    pub is_active: Option<bool>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MusicUploadResponse {
    pub success: bool,
    pub file_path: String,
    pub file_name: String,
    pub file_size: i64,
    pub message: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MusicListResponse {
    pub tracks: Vec<MusicTrack>,
    pub total: i64,
    pub page: i64,
    pub limit: i64,
}

// Query parameters for music listing
#[derive(Debug, Deserialize)]
pub struct MusicQuery {
    pub page: Option<i64>,
    pub limit: Option<i64>,
    pub active_only: Option<bool>,
}
