import type {
  LoginCredentials,
  LoginResponse,
  CreatePostData,
  UpdatePostData,
  CreateMusicTrack,
  UpdateMusicTrack,
  MusicUploadResponse,
  MusicListResponse,
  DashboardStats,
  FileUploadResponse
} from '@/types/admin';
import type { Article } from '@/types/blog';

// Local API Response type to avoid import issues
interface ApiResponse<T = unknown> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

const API_BASE_URL = 'http://localhost:3001/api';

class AdminApiService {
  private getAuthHeaders(): HeadersInit {
    const token = localStorage.getItem('admin_token');
    return {
      'Content-Type': 'application/json',
      'Authorization': token ? `Bearer ${token}` : '',
    };
  }

  private getAuthHeadersForUpload(): HeadersInit {
    const token = localStorage.getItem('admin_token');
    return {
      'Authorization': token ? `Bearer ${token}` : '',
    };
  }

  // Authentication
  async login(credentials: LoginCredentials): Promise<LoginResponse> {
    try {
      const response = await fetch(`${API_BASE_URL}/admin/verify`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${credentials.token}`,
        },
      });

      if (response.ok) {
        localStorage.setItem('admin_token', credentials.token);
        return { success: true };
      } else {
        return { success: false, message: 'Invalid token' };
      }
    } catch (error) {
      return { success: false, message: 'Network error' };
    }
  }

  logout(): void {
    localStorage.removeItem('admin_token');
  }

  isAuthenticated(): boolean {
    return !!localStorage.getItem('admin_token');
  }

  // Dashboard
  async getDashboard(): Promise<DashboardStats> {
    const response = await fetch(`${API_BASE_URL}/admin/dashboard`, {
      headers: this.getAuthHeaders(),
    });

    if (!response.ok) {
      throw new Error('Failed to fetch dashboard data');
    }

    const data = await response.json();
    return data.data || data;
  }

  // Posts Management
  async getPosts(page = 1, limit = 10): Promise<{ posts: Article[]; total: number }> {
    const response = await fetch(
      `${API_BASE_URL}/admin/posts?page=${page}&limit=${limit}`,
      {
        headers: this.getAuthHeaders(),
      }
    );

    if (!response.ok) {
      throw new Error('Failed to fetch posts');
    }

    const data = await response.json();
    return {
      posts: data.posts || [],
      total: data.total || 0,
    };
  }

  async getPost(slug: string): Promise<Article> {
    const response = await fetch(`${API_BASE_URL}/admin/posts/${slug}`, {
      headers: this.getAuthHeaders(),
    });

    if (!response.ok) {
      throw new Error('Failed to fetch post');
    }

    const data = await response.json();
    return data.post || data;
  }

  async createPost(postData: CreatePostData): Promise<ApiResponse> {
    const response = await fetch(`${API_BASE_URL}/admin/posts`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(postData),
    });

    if (!response.ok) {
      throw new Error('Failed to create post');
    }

    return await response.json();
  }

  async updatePost(slug: string, postData: UpdatePostData): Promise<ApiResponse> {
    const response = await fetch(`${API_BASE_URL}/admin/posts/${slug}`, {
      method: 'PUT',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(postData),
    });

    if (!response.ok) {
      throw new Error('Failed to update post');
    }

    return await response.json();
  }

  async deletePost(slug: string): Promise<ApiResponse> {
    const response = await fetch(`${API_BASE_URL}/admin/posts/${slug}`, {
      method: 'DELETE',
      headers: this.getAuthHeaders(),
    });

    if (!response.ok) {
      throw new Error('Failed to delete post');
    }

    return await response.json();
  }

  // Music Management
  async uploadMusic(file: File): Promise<MusicUploadResponse> {
    const formData = new FormData();
    formData.append('music', file);

    const response = await fetch(`${API_BASE_URL}/admin/upload/music`, {
      method: 'POST',
      headers: this.getAuthHeadersForUpload(),
      body: formData,
    });

    if (!response.ok) {
      throw new Error('Failed to upload music file');
    }

    return await response.json();
  }

  async getMusicTracks(page = 1, limit = 10): Promise<MusicListResponse> {
    const response = await fetch(
      `${API_BASE_URL}/admin/music?page=${page}&limit=${limit}`,
      {
        headers: this.getAuthHeaders(),
      }
    );

    if (!response.ok) {
      throw new Error('Failed to fetch music tracks');
    }

    return await response.json();
  }

  async createMusicTrack(trackData: CreateMusicTrack): Promise<ApiResponse> {
    const response = await fetch(`${API_BASE_URL}/admin/music`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(trackData),
    });

    if (!response.ok) {
      throw new Error('Failed to create music track');
    }

    return await response.json();
  }

  async updateMusicTrack(id: number, trackData: UpdateMusicTrack): Promise<ApiResponse> {
    const response = await fetch(`${API_BASE_URL}/admin/music/${id}`, {
      method: 'PUT',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(trackData),
    });

    if (!response.ok) {
      throw new Error('Failed to update music track');
    }

    return await response.json();
  }

  async deleteMusicTrack(id: number): Promise<ApiResponse> {
    const response = await fetch(`${API_BASE_URL}/admin/music/${id}`, {
      method: 'DELETE',
      headers: this.getAuthHeaders(),
    });

    if (!response.ok) {
      throw new Error('Failed to delete music track');
    }

    return await response.json();
  }

  // File Upload
  async uploadImage(file: File): Promise<FileUploadResponse> {
    const formData = new FormData();
    formData.append('image', file);

    const response = await fetch(`${API_BASE_URL}/admin/upload/image`, {
      method: 'POST',
      headers: this.getAuthHeadersForUpload(),
      body: formData,
    });

    if (!response.ok) {
      throw new Error('Failed to upload image');
    }

    return await response.json();
  }
}

export const adminApi = new AdminApiService();
export default adminApi;
