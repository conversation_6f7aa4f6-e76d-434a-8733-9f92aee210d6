pub mod admin;
pub mod auth;
pub mod blog;
pub mod chat;
pub mod health;

// Export specific functions instead of using wildcard imports
pub use admin::{
    ai_assist, create_music_entry, create_post, delete_music_entry, delete_post, get_all_posts,
    get_categories as admin_get_categories, get_dashboard, get_music_files, get_post,
    update_music_entry, update_post, upload_music,
};
pub use auth::verify_token;
pub use blog::{get_categories, get_post_by_slug, get_posts, get_posts_by_category};
pub use chat::chat_with_ai;
pub use health::{health_check, health_detailed, liveness_check, readiness_check};
