// Admin-related type definitions

export interface AdminAuthState {
  isAuthenticated: boolean;
  token: string | null;
  isLoading: boolean;
  error: string | null;
}

export interface LoginCredentials {
  token: string;
}

export interface LoginResponse {
  success: boolean;
  message?: string;
}

// Post Management Types
export interface CreatePostData {
  title: string;
  excerpt: string;
  content: string;
  slug: string;
  categories: string[];
}

export interface UpdatePostData {
  title?: string;
  excerpt?: string;
  content?: string;
  slug?: string;
  categories?: string[];
}

export interface PostFormData {
  title: string;
  excerpt: string;
  content: string;
  slug: string;
  categories: string[];
}

// Music Management Types
export interface MusicTrack {
  id: number;
  title: string;
  artist: string;
  duration: string;
  file_path: string;
  file_name: string;
  file_size: number;
  cover_emoji?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface CreateMusicTrack {
  title: string;
  artist: string;
  duration: string;
  file_path: string;
  file_name: string;
  file_size: number;
  cover_emoji?: string;
  is_active?: boolean;
}

export interface UpdateMusicTrack {
  title?: string;
  artist?: string;
  duration?: string;
  cover_emoji?: string;
  is_active?: boolean;
}

export interface MusicUploadResponse {
  success: boolean;
  file_path: string;
  file_name: string;
  file_size: number;
  message: string;
}

export interface MusicListResponse {
  tracks: MusicTrack[];
  total: number;
  page: number;
  limit: number;
}

// Dashboard Types
export interface DashboardStats {
  total_posts: number;
  total_categories: number;
  total_music_tracks: number;
  recent_posts: Array<{
    id: number;
    title: string;
    date: string;
  }>;
}

// File Upload Types
export interface FileUploadResponse {
  success: boolean;
  file_path: string;
  file_name: string;
  file_size: number;
  message: string;
}

// API Response Types
export interface ApiResponse<T = unknown> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

// Form States
export interface FormState {
  isSubmitting: boolean;
  errors: Record<string, string>;
  success: boolean;
}

// Navigation Types
export interface AdminNavItem {
  id: string;
  label: string;
  icon: string;
  path: string;
  badge?: number;
}
