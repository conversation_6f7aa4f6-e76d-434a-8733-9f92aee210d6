import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import adminApi from '@/services/adminApi';
import './AdminLogin.css';

const AdminLogin: React.FC = () => {
  const [token, setToken] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const navigate = useNavigate();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    try {
      const response = await adminApi.login({ token });
      
      if (response.success) {
        navigate('/admin/dashboard');
      } else {
        setError(response.message || 'Invalid token');
      }
    } catch (err) {
      setError('Network error. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="admin-login">
      <div className="login-container">
        <div className="login-header">
          <md-icon className="login-icon">admin_panel_settings</md-icon>
          <h1 className="login-title md-typescale-headline-large">Admin Login</h1>
          <p className="login-subtitle md-typescale-body-medium">
            Enter your admin token to access the management dashboard
          </p>
        </div>

        <form className="login-form" onSubmit={handleSubmit}>
          <div className="form-field">
            <label htmlFor="token" className="input-label">Admin Token*</label>
            <input
              id="token"
              type="password"
              value={token}
              onChange={(e) => setToken(e.target.value)}
              required
              className="token-input"
              placeholder="Enter admin token..."
            />
          </div>

          {error && (
            <div className="error-message">
              <span className="error-icon">⚠</span>
              <span>{error}</span>
            </div>
          )}

          <button
            type="submit"
            className="login-button"
            disabled={isLoading || !token.trim()}
          >
            {isLoading ? (
              <>
                <span className="loading-spinner">⟳</span>
                Verifying...
              </>
            ) : (
              <>
                <span className="login-icon">→</span>
                Login
              </>
            )}
          </button>
        </form>

        <div className="login-footer">
          <button type="button" onClick={() => navigate('/')}>
            <span className="back-icon">←</span>
            Back to Blog
          </button>
        </div>
      </div>
    </div>
  );
};

export default AdminLogin;
