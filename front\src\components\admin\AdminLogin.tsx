import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import adminApi from '@/services/adminApi';
import './AdminLogin.css';

const AdminLogin: React.FC = () => {
  const [token, setToken] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const navigate = useNavigate();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    try {
      const response = await adminApi.login({ token });
      
      if (response.success) {
        navigate('/admin/dashboard');
      } else {
        setError(response.message || 'Invalid token');
      }
    } catch (err) {
      setError('Network error. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="admin-login">
      <div className="login-container">
        <div className="login-header">
          <md-icon className="login-icon">admin_panel_settings</md-icon>
          <h1 className="login-title md-typescale-headline-large">Admin Login</h1>
          <p className="login-subtitle md-typescale-body-medium">
            Enter your admin token to access the management dashboard
          </p>
        </div>

        <form className="login-form" onSubmit={handleSubmit}>
          <div className="form-field">
            <md-outlined-text-field
              label="Admin Token"
              type="password"
              value={token}
              onInput={(e: any) => setToken(e.target.value)}
              required
              className="token-input"
              placeholder="Enter admin token..."
            />
          </div>

          {error && (
            <div className="error-message">
              <md-icon>error</md-icon>
              <span>{error}</span>
            </div>
          )}

          <md-filled-button
            type="submit"
            className="login-button"
            disabled={isLoading || !token.trim()}
          >
            {isLoading ? (
              <>
                <md-circular-progress indeterminate slot="icon" />
                Verifying...
              </>
            ) : (
              <>
                <md-icon slot="icon">login</md-icon>
                Login
              </>
            )}
          </md-filled-button>
        </form>

        <div className="login-footer">
          <md-text-button onClick={() => navigate('/')}>
            <md-icon slot="icon">arrow_back</md-icon>
            Back to Blog
          </md-text-button>
        </div>
      </div>
    </div>
  );
};

export default AdminLogin;
